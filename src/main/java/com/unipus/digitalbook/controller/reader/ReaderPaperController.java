package com.unipus.digitalbook.controller.reader;

import com.unipus.digitalbook.common.exception.business.BizException;
import com.unipus.digitalbook.controller.BaseController;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.paper.*;
import com.unipus.digitalbook.model.entity.paper.PaperInstance;
import com.unipus.digitalbook.model.entity.paper.PaperVersion;
import com.unipus.digitalbook.model.entity.paper.QueryPaperAnswer;
import com.unipus.digitalbook.model.entity.paper.UserPaperAnswer;
import com.unipus.digitalbook.model.enums.PaperPreviewModeEnum;
import com.unipus.digitalbook.model.enums.ReaderTypeEnum;
import com.unipus.digitalbook.model.enums.ScoreTypeEnum;
import com.unipus.digitalbook.model.params.paper.*;
import com.unipus.digitalbook.service.PaperAnswerService;
import com.unipus.digitalbook.service.PaperInstanceService;
import com.unipus.digitalbook.service.PaperVersionService;
import com.unipus.digitalbook.service.QuestionBankService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("reader/paper")
@Tag(name = "读者试卷作答相关接口", description = "读者试卷作答相关接口")
@Slf4j
public class ReaderPaperController extends BaseController {

    @Resource
    private PaperVersionService paperVersionService;
    @Resource
    private PaperInstanceService paperInstanceService;
    @Resource
    private PaperAnswerService paperAnswerService;
    @Resource
    private QuestionBankService questionBankService;

    @PostMapping("/getPaperVersionByPublishedBook")
    @Operation(summary = "通过教材信息取得试卷版本", description = "通过教材信息取得试卷版本")
    public Response<PaperVersionDTO> getPaperVersionByPublishedBook(@RequestBody PaperInitParam param) {
        PaperVersion paperVersion = paperVersionService.getPaperVersionByBookInfo(
                param.getBookId(), param.getBookVersionNumber(), param.getPaperId());
        return Response.success(new PaperVersionDTO(param.getBookId(), param.getBookVersionNumber(),  paperVersion));
    }

    @PostMapping("/getChallengePaperStatInfo")
    @Operation(summary = "查询挑战卷统计信息", description = "查询挑战卷统计信息")
    public Response<QuestionBankStatDTO> getChallengePaperStatInfo(@RequestBody BankQueryParam param) {
        return Response.success(QuestionBankStatDTO.build(
                questionBankService.getQuestionBankStatInfo(param.getPaperId(), param.getVersionNumber())));
    }

    @PostMapping("/instance/createPreviewPaperInstanceByPublishedBook")
    @Operation(summary = "生成预览试卷实例(仅老师预览用)", description = "生成预览试卷实例(仅老师预览用)")
    public Response<PaperInstanceDTO> createPreviewPaperInstanceByPublishedBook(@RequestBody PaperInitParam param) {
        if(!ReaderTypeEnum.TEACHER.match(getReaderType())){
            throw new BizException("学生用户不能生成预览试卷");
        }

        // 取得试卷版本信息
        PaperVersion paperVersion = paperVersionService.getPaperVersionByBookInfo(
                param.getBookId(), param.getBookVersionNumber(), param.getPaperId());

        // 设置预览模式：读者接口教师身份预览，仅仅查看教师模式
        PaperInstance paperInstance = param.toEntity(paperVersion.getVersionNumber(), getUserAccessInfo(),
                PaperPreviewModeEnum.TEACHER);

        // 生成试卷实例
        return Response.success(new PaperInstanceDTO(paperInstanceService.generatePreviewPaperInstance(
                paperInstance), true));
    }

    @PostMapping("/instance/createPaperInstanceByPublishedBook")
    @Operation(summary = "通过教材信息创建试卷实例(学生作答无答案返回，老师作答有答案返回)", description = "通过教材信息创建试卷实例")
    public Response<PaperInstanceDTO> createPaperInstanceByBookVersion(@RequestBody PaperInitParam param) {
        boolean isReturnAnswer = ReaderTypeEnum.TEACHER.match(getReaderType());

        // 通过教材版本取得试卷版本
        PaperVersion paperVersion = paperVersionService.getPaperVersionByBookInfo(
                param.getBookId(), param.getBookVersionNumber(), param.getPaperId());
        PaperInstance paperInstance = param.toEntity(paperVersion.getVersionNumber(), getUserAccessInfo(), null);

        // 创建并返回试卷实例
        return Response.success(new PaperInstanceDTO(paperInstanceService.generateRealPaperInstance(paperInstance), isReturnAnswer));
    }

    @PostMapping("/instance/getLatestPaperInstance")
    @Operation(summary = "获取用户特定试卷的最近一次试卷实例信息", description = "获取用户特定试卷的最近一次试卷实例信息")
    public Response<PaperInstanceDTO> getLatestPaperInstance(@RequestBody PaperInstanceParam param) {
        // 创建并返回试卷实例
        PaperInstance entity = param.toEntity(getUserAccessInfo());
        PaperInstance paperInstance = paperInstanceService.getLatestPaperInstance(entity, null);
        return Response.success(PaperInstanceDTO.build(paperInstance, false));
    }

    @GetMapping("/instance/getPaperAnalysis")
    @Operation(summary = "取得试卷作答解析", description = "取得试卷作答解析")
    public Response<PaperAnalysisDTO> getPaperAnalysis(@RequestParam("instanceId") String instanceId) {
        long startTime = System.currentTimeMillis();
        try {
            // 取得试卷解析（内部已优化，避免重复查询PaperScoreBatch）
            long loadInstanceStart = System.currentTimeMillis();
            PaperInstance paperInstance = paperInstanceService.getPaperAnalysis(instanceId);
            long loadInstanceTime = System.currentTimeMillis() - loadInstanceStart;

            // 取得用户作答
            long getUserAnswerStart = System.currentTimeMillis();
            QueryPaperAnswer param = new QueryPaperAnswer(instanceId, paperInstance.getPaperType(), getUserAccessInfo());
            UserPaperAnswer userPaperAnswer = paperAnswerService.getUserPaperAnswer(param);
            long getUserAnswerTime = System.currentTimeMillis() - getUserAnswerStart;

            // 构建响应DTO
            long buildDTOStart = System.currentTimeMillis();
            PaperAnalysisDTO result = new PaperAnalysisDTO(paperInstance, userPaperAnswer);
            long buildDTOTime = System.currentTimeMillis() - buildDTOStart;

            long totalTime = System.currentTimeMillis() - startTime;
            log.info("getPaperAnalysis performance - instanceId: {}, loadInstance: {}ms, getUserAnswer: {}ms, buildDTO: {}ms, total: {}ms",
                    instanceId, loadInstanceTime, getUserAnswerTime, buildDTOTime, totalTime);

            return Response.success(result);
        } catch (Exception e) {
            long totalTime = System.currentTimeMillis() - startTime;
            log.error("getPaperAnalysis failed - instanceId: {}, time: {}ms, error: {}", instanceId, totalTime, e.getMessage(), e);
            throw e;
        }
    }

    @PostMapping("/answer/saveUserPaperAnswer")
    @Operation(summary = "保存用户作答记录", description = "保存用户作答记录")
    public Response<UserPaperScoreDTO> saveUserPaperAnswer(@RequestBody UserPaperAnswerParam param) {
        return Response.success(new UserPaperScoreDTO(paperAnswerService.saveUserPaperAnswer(param, getUserAccessInfo())));
    }

    @PostMapping("/answer/getUserPaperAnswer")
    @Operation(summary = "取得用户作答记录", description = "取得用户作答记录")
    public Response<UserPaperAnswerDTO> getUserPaperAnswer(@RequestBody QueryPaperAnswerParam param) {
        return Response.success(UserPaperAnswerDTO.build(paperAnswerService.getUserPaperAnswer(param.toEntity(getUserAccessInfo()))));
    }

    @PostMapping("/answer/submitUserPaperAnswer")
    @Operation(summary = "提交试卷作答记录", description = "提交试卷作答记录")
    public Response<UserPaperScoreDTO> submitUserPaperAnswer(@RequestBody UserPaperAnswerParam param) {
        return Response.success(new UserPaperScoreDTO(paperAnswerService.submitUserPaperAnswer(param, getUserAccessInfo())));
    }

    @PostMapping("/answer/submitUserChallengeScore")
    @Operation(summary = "提交用户挑战卷最佳成绩", description = "提交用户挑战卷最佳成绩")
    public Response<UserPaperScoreDTO> submitUserChallengeScore(@RequestBody UserPaperInfoParam param) {
        return Response.success(new UserPaperScoreDTO(
                paperAnswerService.submitUserChallengeScore(param.toEntity(getUserAccessInfo()), getUserAccessInfo())));
    }

    @PostMapping("/answer/getUserPaperScore")
    @Operation(summary = "取得用户试卷成绩(评价)", description = "取得用户试卷成绩(评价)")
    public Response<UserPaperScoreDTO> getUserPaperScore(@RequestBody UserPaperInfoParam param) {
        return Response.success(UserPaperScoreDTO.build(
                paperAnswerService.getUserPaperScore(param.toEntity(getUserAccessInfo()), getUserAccessInfo()), ScoreTypeEnum.PERCENTAGE));
    }

    @PostMapping("/answer/getUserPaperSubmitHistory")
    @Operation(summary = "取得用户试卷成绩提交记录", description = "取得用户试卷成绩提交记录")
    public Response<PaperScoreRecordListDTO> getUserPaperSubmitHistory(@RequestBody UserPaperInfoParam param) {
        return Response.success(PaperScoreRecordListDTO.build(
                paperAnswerService.getUserPaperSubmitHistory(param.toEntity(getUserAccessInfo())), ScoreTypeEnum.PERCENTAGE));
    }

}
