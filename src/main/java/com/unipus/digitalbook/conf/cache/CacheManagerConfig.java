package com.unipus.digitalbook.conf.cache;

import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.time.Duration;
import java.util.List;
import java.util.Objects;

@Configuration
@EnableCaching
public class CacheManagerConfig {

    public static final String CACHE_NAME_PAPER_VERSION = "paperVersion";
    public static final String CACHE_NAME_PAPER_QUESTION = "PaperService:questionGroup";
    public static final String CACHE_NAME_PAPER_BANK_QS = "ChallengePaperInstanceStrategy::questionBankList";
    public static final String CACHE_NAME_PAPER_DETAIL = "PaperService:paperDetail";
    public static final String CACHE_NAME_PAPER_TAGS = "PaperService:tagProcessor";
    public static final String CACHE_NAME_PAPER_SCORE_TEMPLATE = "paperScoreTemplate";

    @Bean
    @Primary
    public CacheManager cacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();

        // 配置默认的Caffeine实例
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .maximumSize(1000)
                .expireAfterWrite(Duration.ofDays(1)));

        // 注册试卷版本缓存 - 支持访问时自动续期
        cacheManager.registerCustomCache(CACHE_NAME_PAPER_VERSION,
                Caffeine.newBuilder()
                        .maximumSize(1000)
                        .expireAfterAccess(Duration.ofDays(1))  // 访问后1天过期，实现自动续期
                        .expireAfterWrite(Duration.ofDays(7))   // 写入后7天强制过期，防止数据过期
                        .build());

        // 注册试卷题目列表缓存 - 支持访问时自动续期
        cacheManager.registerCustomCache(CACHE_NAME_PAPER_QUESTION,
                Caffeine.newBuilder()
                        .maximumSize(1000)
                        .expireAfterAccess(Duration.ofDays(1))
                        .expireAfterWrite(Duration.ofDays(1))
                        .build());

        // 注册题库题目列表缓存 - 支持访问时自动续期
        cacheManager.registerCustomCache(CACHE_NAME_PAPER_BANK_QS,
                Caffeine.newBuilder()
                        .maximumSize(1000)
                        .expireAfterAccess(Duration.ofDays(1))
                        .expireAfterWrite(Duration.ofDays(1))
                        .build());

        // 注册试卷详情缓存 - 支持访问时自动续期
        cacheManager.registerCustomCache(CACHE_NAME_PAPER_DETAIL,
                Caffeine.newBuilder()
                        .maximumSize(1000)
                        .expireAfterAccess(Duration.ofDays(1))
                        .expireAfterWrite(Duration.ofDays(1))
                        .build());

        // 注册试卷题目标签缓存 - 支持访问时自动续期
        cacheManager.registerCustomCache(CACHE_NAME_PAPER_TAGS,
                Caffeine.newBuilder()
                        .maximumSize(1000)
                        .expireAfterAccess(Duration.ofDays(1))
                        .expireAfterWrite(Duration.ofDays(1))
                        .build());

        // 注册试卷题评价模板缓存 - 支持访问时自动续期
        cacheManager.registerCustomCache(CACHE_NAME_PAPER_SCORE_TEMPLATE,
                Caffeine.newBuilder()
                        .maximumSize(1000)
                        .expireAfterAccess(Duration.ofDays(1))
                        .expireAfterWrite(Duration.ofDays(1))
                        .build());

        return cacheManager;
    }

    public <T> T getLocalCatchData(String cacheName, String key) {
        if (key == null) {
            return null;
        }
        Cache cache = cacheManager().getCache(cacheName);
        if (cache == null) {
            return null;
        }
        Cache.ValueWrapper wrapper = cache.get(key);
        if (wrapper == null) {
            return null;
        }
        @SuppressWarnings("unchecked")
        T cachedData = (T) wrapper.get();
        return cachedData;
    }

    public void addLocalCatchData(String cacheName, String key, Object value){
        if(key==null || value==null || value instanceof List<?> dataList && dataList.isEmpty() ){
            return;
        }
        Objects.requireNonNull(cacheManager().getCache(cacheName)).put(key, value);
    }
}
